# PayrollGrid API Response Documentation

## Overview

The `readPayrollGrid()` method in `engine/APIClasses/Payroll/PayrollGrid.php` returns a JSON response containing payroll data for owners, contracts, and rent payments. This document provides a comprehensive reference for all properties in the API response.

## Main Response Structure

```json
{
  "rows": [],      // Array of payroll records
  "total": 0,      // Integer: Total count of all results
  "footer": []     // Array containing summary/totals data
}
```

## Row Record Properties

Each record in the `rows` array contains the following properties, organized by logical groups:

### Owner/Contract Information

| Property | Type | Description |
|----------|------|-------------|
| `owner_id` | integer | Unique identifier for the owner |
| `owner_type` | integer | Type of owner (1 = individual, other = company) |
| `owner_names` | string | Full name (for individuals) or company name |
| `egn_eik` | string | Personal ID (EGN) for individuals or company ID (EIK) |
| `first_name` | string | First name (individuals only) |
| `surname` | string | Surname (individuals only) |
| `lastname` | string | Last name (individuals only) |
| `phone` | string | Phone number |
| `mobile` | string | Mobile phone number |
| `iban` | string | Bank account IBAN |
| `rep_names` | string | Representative names (HTML formatted with `<br/>`) |
| `rent_place` | string | Rental location/place |
| `land` | string | Land/location name (processed from rent_place) |
| `is_dead` | boolean | Whether the owner is deceased |
| `is_heritor` | boolean | Whether this record represents a heritor |

### Area and Land Data

| Property | Type | Description |
|----------|------|-------------|
| `area` | string | Total area (formatted to 3 decimal places) |
| `cultivated_area` | string | Cultivated area (formatted to 3 decimal places) |
| `pu_area` | string | Personal use area (formatted to 3 decimal places) |
| `has_personal_use` | boolean | Whether owner has personal use areas |
| `mestnost` | string | Locality/region (or "-" if not available) |
| `category` | string | Plot category (or "-" if not available) |
| `c_type` | string | Contract type name |
| `area_type` | string | Area type description |

### Contract Data

| Property | Type | Description |
|----------|------|-------------|
| `farming` | integer | Farming ID |
| `contract_array` | array | Array of contract IDs |
| `c_num_array` | array | Array of contract numbers |
| `sv_num_array` | array | Array of SV numbers |
| `sv_num` | string | SV number (or "-" if not available) |
| `sv_date` | string | SV date (or "-" if not available) |
| `plots_array` | array | Array of plot IDs |
| `plots_name_array` | array | Array of plot names/identifiers |

### Financial Data (Money)

| Property | Type | Description |
|----------|------|-------------|
| `renta` | string | Contract rent amount (formatted to 2 decimal places) |
| `contract_renta` | numeric | Raw contract rent amount |
| `charged_renta` | string | Charged rent amount (formatted to 2 decimal places or "-") |
| `paid_renta` | string | Paid rent amount (formatted to 2 decimal places or null) |
| `unpaid_renta` | string | Unpaid rent amount (formatted to 2 decimal places) |
| `over_paid` | string | Overpaid amount (formatted to 2 decimal places) |
| `converted_renta_nat` | numeric | Converted natura rent to money value |

### Natura (In-Kind) Rent Data

| Property | Type | Description |
|----------|------|-------------|
| `renta_nat` | array | Contract rent in natura by type ID |
| `renta_nat_text` | string | Formatted text of contract rent in natura (HTML with `<br/>`) |
| `charged_renta_nat` | array | Charged rent in natura by type ID |
| `charged_renta_nat_text` | string | Formatted text of charged rent in natura (HTML with `<br/>`) |
| `paid_renta_nat` | string | Paid rent in natura (formatted text or "-") |
| `paid_renta_nat_details` | array | Detailed paid rent in natura by type ID |
| `unpaid_renta_nat` | string | Unpaid rent in natura (formatted text with `</br>`) |
| `unpaid_renta_nat_arr` | array | Unpaid rent in natura by type ID |
| `over_paid_nat` | string | Overpaid rent in natura (formatted text with `</br>`) |
| `over_paid_renta_nat_arr` | array | Overpaid rent in natura by type ID |
| `renta_nat_type` | string | Types of natura rent (formatted text with `</br>`) |
| `unpaid_renta_nat_unit_value` | string | Unit values for unpaid natura rent |

### Payment Details

| Property | Type | Description |
|----------|------|-------------|
| `paid_renta_by` | string | How rent was paid (formatted text with `</br>` or "-") |
| `paid_renta_by_arr` | array | Payment methods array |
| `paid_renta_nat_by` | string | How natura rent was paid (formatted text or "-") |
| `paid_renta_nat_by_arr` | array | Natura payment methods array |
| `paid_renta_nat_by_detailed` | string | Detailed natura payments (formatted text or "-") |
| `total_by_renta` | string | Total payments in money (formatted with " лв." or "-") |
| `total_by_renta_sum` | numeric | Raw total payments in money |
| `total_by_renta_nat` | string | Total payments in natura (formatted text or "-") |
| `total_by_renta_nat_arr` | array | Total payments in natura by type |

### Complex Nested Data Structures

| Property | Type | Description |
|----------|------|-------------|
| `plots_contracts_area_array` | array | Plot-contract-area relationships with pc_id, c_num, plot_name, area |
| `plots_contracts_renta` | array | Rent amounts by plot and contract |
| `plots_contracts_charged_renta` | array | Charged rent amounts by plot and contract |
| `plots_contracts_renta_nat` | array | Natura rent by plot, contract, and type |
| `plots_contracts_charged_renta_nat` | array | Charged natura rent by plot, contract, and type |
| `owner_plots_percent` | array | Owner percentage by contract ID and plot ID |
| `paid_renta_by_contract` | array | Paid rent amounts by contract ID |
| `paid_renta_nat_by_contract` | array | Paid natura rent by contract ID and type |

### UI/Display Properties

| Property | Type | Description |
|----------|------|-------------|
| `id` | integer | Grid row ID (iterator value) |
| `iconCls` | string | CSS class for tree icons ("icon-tree-user", "icon-tree-user-rip", "icon-tree-users") |
| `children` | array | Child records for heritors (recursive structure with same properties) |

## Footer Structure

The `footer` array contains a single summary object with aggregated totals:

| Property | Type | Description |
|----------|------|-------------|
| `area_type` | string | Label (usually "<b>ОБЩО</b>" meaning "TOTAL") |
| `area` | string | Total area across all records (formatted to 3 decimal places) |
| `pu_area` | string | Total personal use area (formatted to 3 decimal places) |
| `renta` | string | Total contract rent (formatted to 2 decimal places) |
| `charged_renta` | string | Total charged rent (formatted to 2 decimal places) |
| `paid_renta` | string | Total paid rent (formatted to 2 decimal places) |
| `unpaid_renta` | string | Total unpaid rent (formatted to 2 decimal places) |
| `renta_nat_text` | string | Total natura rent summary (HTML formatted) |
| `charged_renta_nat_text` | string | Total charged natura rent summary (HTML formatted) |
| `paid_renta_nat` | string | Total paid natura rent summary (HTML formatted) |
| `paid_renta_nat_by` | string | Total paid natura rent by method summary |
| `paid_renta_nat_by_detailed` | string | Detailed total paid natura rent summary |
| `total_by_renta` | string | Total payments in money (formatted to 2 decimal places) |
| `total_by_renta_nat` | string | Total payments in natura summary |
| `renta_nat_type` | string | Summary of natura rent types |

## Data Types and Formatting Notes

- **Numeric values**: Most financial amounts are formatted as strings with specific decimal places
- **Arrays**: Complex nested structures use arrays with specific key-value patterns
- **HTML formatting**: Many text fields include HTML tags (`<br/>`, `</br>`, `<b>`) for display
- **Null handling**: Missing or zero values often display as "-" or null
- **Recursive structure**: Records with heritors include a `children` array with the same structure
- **Natura rent**: Rent paid in goods/products rather than money, tracked separately with unit values

## Relationships Between Properties

- `owner_id` links to heritor records through the `children` array
- `contract_array` contains IDs that relate to various contract-specific data
- `plots_array` contains plot IDs that appear in nested plot-contract relationships
- Financial totals are calculated from individual contract and plot amounts
- Natura rent types are referenced by ID in various `_nat` properties
